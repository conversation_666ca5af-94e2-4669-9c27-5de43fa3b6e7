{"common": {"loading": "Loading...", "error": "Error occurred", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "with": "with", "and": " and ", "traits": " traits", "retry": "Retry"}, "nav": {"home": "Home", "create": "Create Story", "myStories": "My Stories", "pricing": "Pricing", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "startCreating": "Start Creating", "credits": "Credits"}, "home": {"hero": {"title": "Create Unique Stories", "titleHighlight": "for Your Child", "subtitle": "AI-powered personalized children's audiobook creation platform. Let every child have their own unique stories with beautiful illustrations and vivid narration.", "startCreating": "Start Creating", "watchDemo": "Watch Demo"}, "stats": {"storiesCreated": "Stories Created", "activeUsers": "Active Users", "userRating": "User Rating", "awards": "Awards Won", "thisMonth": "this month"}, "features": {"title": "Why Choose StoryWeaver?", "subtitle": "We combine advanced AI technology with child psychology to create unique story experiences for every child", "aiCreation": {"title": "AI Smart Creation", "description": "Based on advanced AI technology, create personalized story content according to the child's age, interests and personality traits"}, "illustrations": {"title": "Beautiful Illustrations", "description": "AI-generated high-quality illustrations with rich colors and diverse styles, adding visual charm to stories"}, "narration": {"title": "Warm Narration", "description": "Professional voice synthesis technology provides warm and intimate reading experience, making stories more vivid"}, "learnMore": "Learn More", "viewSamples": "View Samples", "listenVoice": "Listen Voice"}, "howItWorks": {"title": "Create Your Story in Three Steps", "subtitle": "Simple steps to create a unique story for your child", "step1": {"title": "Set Character", "description": "Enter your child's name, age and personality traits, let AI understand your little protagonist"}, "step2": {"title": "Choose <PERSON>", "description": "Choose from various themes including adventure, friendship, learning and other suitable story types"}, "step3": {"title": "Generate Story", "description": "AI will create a complete story including text, illustrations and voice narration"}}, "cta": {"title": "Ready to Create a Unique Story for Your Child?", "subtitle": "Join thousands of parents and let AI create unique story experiences for your children", "startNow": "Start Creating Now", "viewPricing": "View Pricing Plans"}}, "pricing": {"title": "Choose the Right", "titleHighlight": "Pricing Plan", "subtitle": "Whether you use occasionally or create frequently, we have the right plan for you. All plans include AI story creation, beautiful illustrations and voice narration.", "monthly": "Monthly", "yearly": "Yearly", "save": "Save 17%", "mostPopular": "Most Popular", "perMonth": "/month", "perYear": "/year", "plans": {"free": {"name": "Free Trial", "description": "Perfect for first-time users", "features": ["50 credits per month", "Basic story templates", "Standard quality illustrations", "Basic voice narration", "Online reading", "Community support"], "limitations": ["No download support", "No advanced customization", "With watermark"]}, "basic": {"name": "Basic Plan", "description": "Perfect for occasional use families", "features": ["200 credits per month", "All story templates", "HD illustrations", "Multiple voice options", "Unlimited online reading", "Download support", "No watermark", "Email support"]}, "premium": {"name": "Premium Plan", "description": "Perfect for frequent use families", "features": ["500 credits per month", "All story templates", "Ultra HD illustrations", "Professional voice narration", "Unlimited online reading", "Download support", "No watermark", "Advanced customization options", "Priority customer support", "Family sharing (up to 5 people)"]}, "family": {"name": "Family Plan", "description": "Perfect for multi-child families", "features": ["1000 credits per month", "All story templates", "Ultra HD illustrations", "Professional voice narration", "Unlimited online reading", "Download support", "No watermark", "Advanced customization options", "Dedicated customer support", "Family sharing (up to 10 people)", "Physical book creation service", "Dedicated story consultant"]}}, "selectPlan": "Select This Plan", "freeStart": "Start Free", "faq": {"title": "Frequently Asked Questions", "questions": [{"question": "What are credits? How to use them?", "answer": "Credits are consumption units for creating stories. Creating a complete story (including text, illustrations and voice) requires approximately 30-50 credits."}, {"question": "Can I cancel subscription anytime?", "answer": "Yes, you can cancel your subscription at any time. After cancellation, you can still use remaining credits, but won't be renewed."}, {"question": "Do credits expire?", "answer": "Credits are valid for 12 months after purchase. Monthly credits for subscribers are valid within the current month."}, {"question": "What payment methods are supported?", "answer": "We support Alipay, WeChat Pay, bank cards and other payment methods, safe and convenient."}]}}, "create": {"title": "Create Your Unique Story", "steps": {"character": "Character Setup", "theme": "Theme Selection", "style": "Style Configuration", "preview": "Preview & Confirm", "audio": "Audio Options"}, "character": {"title": "Character Setup", "subtitle": "Create the protagonist of your story", "characterName": "Character Name", "characterNamePlaceholder": "Enter character name", "characterNameHelper": "This name will be used throughout the story", "characterAge": "Character Age", "ageDescription": "Selecting character age will affect story language complexity and content depth", "ageLabel": "{{age}} years old", "characterTraits": "Character Traits", "selectTraits": "Select up to 3 traits", "selectTraitsDescription": "Select 1-5 traits to shape character personality (selected {{selected}}/{{max}})", "personalityTraits": "Personality Traits", "abilities": "Special Abilities", "interests": "Interests", "characterPreview": "Character Preview", "traits": {"brave": "Brave", "kind": "Kind", "curious": "Curious", "funny": "Funny", "smart": "Smart", "creative": "Creative", "adventurous": "Adventurous", "helpful": "Helpful", "musical": "Musical", "athletic": "Athletic", "artistic": "Artistic", "magical": "Magical", "animals": "Loves Animals", "nature": "Loves Nature", "books": "Loves Reading", "science": "Science Explorer"}, "ageDescriptions": {"3": "Simple vocabulary, basic concepts", "4": "Short expressions, daily scenarios", "5": "Complete sentences, simple plots", "6": "Complex vocabulary, moral concepts", "7": "Logical thinking, problem solving", "8": "Deep plots, complex relationships"}}, "theme": {"title": "Theme Selection", "subtitle": "Choose the theme and setting of your story", "storyTheme": "Story Theme", "storySetting": "Story Setting", "themeDescription": "Choose a theme to determine the core content and values of the story", "settingDescription": "Choose where the story takes place to create the right environment for characters", "popularThemes": "Popular Themes", "moreThemes": "More Themes", "preview": {"title": "Theme Preview", "description": "A {{theme}} story taking place in {{setting}}"}, "themes": {"adventure": "Adventure Quest", "friendship": "Friendship Growth", "family": "Family Warmth", "learning": "Learning Growth", "magic": "Magic Fantasy", "animals": "Animal World", "nature": "Nature Conservation", "space": "Space Sci-Fi"}, "themeDescriptions": {"adventure": "Brave adventures and magical discoveries", "friendship": "Stories about friendship and teamwork", "family": "Warm family life stories", "learning": "Stories of growth through learning", "magic": "A world full of magic and miracles", "animals": "Fun stories of cute animals", "nature": "Environmental protection and love of nature", "space": "Sci-fi adventures exploring the universe"}, "settings": {"forest": "Mysterious Forest", "castle": "Fairy Tale Castle", "ocean": "Deep Sea World", "space": "Space Station", "village": "Cozy Village", "school": "Happy School", "garden": "Garden Paradise", "mountain": "Mountain Peaks"}, "settingDescriptions": {"forest": "Ancient forest full of magic", "castle": "Beautiful princess castle", "ocean": "Magical underwater kingdom", "space": "Futuristic space base", "village": "Harmonious and friendly town", "school": "Campus full of laughter", "garden": "Beautiful garden world", "mountain": "Magnificent mountain scenery"}}, "style": {"title": "Style Configuration", "subtitle": "Set the visual style and voice narration", "visualStyle": "Story Style", "voiceNarration": "Voice Narration", "styleDescription": "Choose the narrative style of the story, which will affect language expression and plot development", "recommendedStyles": "Recommended Styles", "moreStyles": "More Styles", "voiceDescription": "Choose the voice type for story narration to make the story more vivid and interesting", "recommendedVoices": "Recommended Voices", "moreVoices": "More Voices", "playing": "Playing...", "listen": "Listen", "preview": {"title": "Style Preview", "description": "Using {{style}} style with {{voice}} narration"}, "styles": {"cartoon": "Cartoon", "watercolor": "Watercolor", "sketch": "Sketch", "fantasy": "Fantasy Magic", "realistic": "Realistic", "anime": "Anime", "modern": "Modern Life", "fairy-tale": "Classic Fairy Tale", "adventure": "Adventure Quest", "educational": "Educational Fun", "rhyme": "Rhythmic Poetry"}, "styleDescriptions": {"fairy-tale": "Traditional fairy tale style, warm and imaginative", "adventure": "Exciting adventure stories full of discovery", "educational": "Learning knowledge through fun stories", "rhyme": "Rhythmic poetry-style stories", "modern": "Real stories close to modern life", "fantasy": "Fantasy world full of magic and magical creatures"}, "stylePreviews": {"fairy-tale": "Once upon a time, in a faraway place...", "adventure": "Suddenly, a mysterious voice came...", "educational": "Let's explore this interesting phenomenon together...", "rhyme": "Birds sing so beautifully, flowers bloom so prettily...", "modern": "Today is a special day...", "fantasy": "The light of magic flickers in the air..."}, "voices": {"warm-female": "Warm Female", "gentle-male": "Gentle Male", "child-like": "Child Voice", "storyteller": "Storyteller", "gentle_female": "Gentle Female", "warm_male": "Warm Male", "child_friendly": "Child Friendly", "leda": "Gentle Female", "puck": "Energetic Male", "charon": "Steady Male", "achernar": "Soft Female", "energetic": "Energetic Youth"}, "voiceDescriptions": {"leda": "Young and lively female voice, suitable for children's stories", "puck": "Optimistic and upbeat male voice, full of energy", "charon": "Deep and steady male voice, full of wisdom", "achernar": "Gentle and soft female voice, warm as spring breeze", "energetic": "Energetic young voice, suitable for adventure stories"}, "voiceAccents": {"leda": "Standard Mandarin", "puck": "Standard Mandarin", "charon": "Standard Mandarin", "achernar": "Standard Mandarin", "energetic": "Standard Mandarin"}}, "audio": {"title": "Audio Options", "subtitle": "Configure voice narration settings"}, "preview": {"title": "Your Unique Story is About to Be Born!", "subtitle": "Please confirm the following settings, we will create a unique story for you", "storyBackground": "Story Background", "theme": "Theme", "setting": "Setting", "storyStyle": "Story Style", "voiceNarration": "Voice Narration", "noVoice": "No Voice Narration", "characterInfo": "Character Info", "estimatedTime": "Estimated Generation Time", "estimatedTimeSubtitle": "AI creation takes some time", "estimatedTimes": {"text": "1-2 minutes", "images": "3-5 minutes", "audio": "2-3 minutes", "total": "6-10 minutes"}, "creditConsumption": "Credit Consumption", "creditConsumptionSubtitle": "Credits required for story creation", "storyText": "Story Text", "illustrations": "Illustrations", "audioSynthesis": "Audio Synthesis", "totalTime": "Total Time", "totalConsumption": "Total Consumption", "credits": "Credits", "sufficientCredits": "Sufficient Credits", "insufficientCredits": "Insufficient Credits", "currentCredits": "You currently have {{credits}} credits, enough to create this story", "needMoreCredits": "You currently have {{credits}} credits, need {{needed}} more credits", "buyCredits": "Buy Credits", "storyPreview": "Story Preview", "storyDescription": "This is a {{theme}} story about {{characterName}}. {{characterName}} is a {{age}} year old child, {{traits}} The story will unfold in {{setting}}, using {{style}} narrative style, with warm narration by {{voice}}.", "termsNotice": "By clicking 'Start Creating', you agree to consume the corresponding credits to generate story content. Please do not close the page during generation to avoid affecting the creation progress.", "startCreating": "Start Creating", "ageYears": "{{age}} years old", "moreTraits": "+{{count}} more traits"}, "stepProgress": "Step {{current}} / {{total}}", "newStory": "Create New Story", "testBackend": "Test Backend", "currentCredits": "Current Credits", "confirmLeave": "You have unsaved drafts, are you sure you want to leave?", "success": {"title": "Story Creation Successful", "message": "Generating exciting story content for you...", "generating": "Your story is being generated, please wait..."}, "error": {"title": "Creation Failed", "message": "Please try again later", "serviceUnavailable": "Story creation service unavailable"}}, "subscription": {"title": "Subscription Info", "currentPlan": "Current Plan", "currentPlanFeatures": "Current Plan Features", "validUntil": "<PERSON>id <PERSON>", "upgrade": "Upgrade Plan", "subscribe": "Subscribe", "manage": "Manage Subscription", "aiModel": "AI Model", "imageQuality": "Image Quality", "audioQuality": "Audio Quality", "maxStories": "Max Stories", "maxPages": "<PERSON>", "customCharacters": "Custom Characters", "support": "Support", "commercialUse": "Commercial Use", "apiAccess": "API Access", "prioritySupport": "Priority Support", "exportFormats": "Export Formats", "status": {"active": "Active", "canceled": "Canceled", "pastDue": "Past Due", "unknown": "Unknown"}, "features": {"free": {"aiModel": "Basic AI Model", "imageQuality": "Standard Quality", "audioQuality": "Standard Quality", "maxStories": "{{count}} stories/month", "maxPages": "{{count}} pages/story", "customCharacters": "{{count}} custom characters", "support": "Community Support"}, "basic": {"aiModel": "Enhanced AI Model", "imageQuality": "High Quality", "audioQuality": "High Quality", "maxStories": "{{count}} stories/month", "maxPages": "{{count}} pages/story", "customCharacters": "{{count}} custom characters", "support": "Email Support"}, "pro": {"aiModel": "Advanced AI Model", "imageQuality": "Premium Quality", "audioQuality": "Premium Quality", "maxStories": "{{count}} stories/month", "maxPages": "{{count}} pages/story", "customCharacters": "{{count}} custom characters", "support": "Priority Support"}, "unlimited": {"aiModel": "Ultra AI Model", "imageQuality": "Ultra Quality", "audioQuality": "Ultra Quality", "maxStories": "Unlimited stories", "maxPages": "{{count}} pages/story", "customCharacters": "{{count}} custom characters", "support": "24/7 Priority Support"}}, "plans": {"free": "Free Plan", "basic_monthly": "Basic Plan", "pro_monthly": "Pro Plan", "unlimited_monthly": "Unlimited Plan"}}, "footer": {"description": "AI-powered personalized children's audiobook creation platform that allows parents and children to create unique storybooks with beautiful illustrations and vivid narration.", "product": "Product", "support": "Support", "physicalBooks": "Physical Books", "helpCenter": "Help Center", "contact": "Contact Us", "faq": "FAQ", "tutorials": "Tutorials", "copyright": "All rights reserved.", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "madeWith": "Made with", "madeWithLove": ""}}