export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  credits: number;
  createdAt: string;
  updatedAt: string;
  subscription?: UserSubscription;
}

export interface UserProfile extends User {
  subscription?: UserSubscription;
  stats: UserStats;
  preferences: UserPreferences;
}

export interface UserSubscription {
  id: string;
  plan: string | SubscriptionPlan;
  status: 'active' | 'canceled' | 'expired' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  stripeSubscriptionId?: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  maxStories: number | null; // null means unlimited
  maxCredits: number | null; // null means unlimited
  priority: number; // for sorting plans
}

export interface UserStats {
  totalStories: number;
  completedStories: number;
  totalBooksOrdered: number;
  accountAge: number; // days since account creation
  lastLoginAt?: string;
}

export interface UserPreferences {
  language: string;
  timezone: string;
  emailNotifications: {
    storyCompleted: boolean;
    bookShipped: boolean;
    promotions: boolean;
    newsletter: boolean;
  };
  defaultStorySettings: {
    style: string;
    voice: string;
    theme: string;
  };
}

export interface LoginCredentials {
  googleToken?: string;
  code?: string;
  redirectUri?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Form types for user management
export interface UpdateProfileForm {
  name: string;
  avatar?: File;
}

export interface UpdatePreferencesForm {
  language: string;
  timezone: string;
  emailNotifications: UserPreferences['emailNotifications'];
  defaultStorySettings: UserPreferences['defaultStorySettings'];
}

// Google OAuth types
export interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
}

export interface GoogleAuthResponse {
  credential: string;
  select_by: string;
}

// Credit system types
export interface CreditTransaction {
  id: string;
  userId: string;
  type: 'purchase' | 'usage' | 'refund' | 'bonus';
  amount: number; // positive for credits added, negative for credits used
  description: string;
  storyId?: string; // if related to story creation
  paymentId?: string; // if related to payment
  createdAt: string;
}

export interface CreditBalance {
  current: number;
  pending: number; // credits that are being processed
  lifetime: number; // total credits ever purchased
}

// User activity types
export interface UserActivity {
  id: string;
  userId: string;
  type: 'story_created' | 'story_completed' | 'book_ordered' | 'credits_purchased' | 'subscription_started';
  description: string;
  metadata?: Record<string, any>;
  createdAt: string;
}

// Account management types
export interface DeleteAccountRequest {
  password?: string;
  reason?: string;
  feedback?: string;
}

export interface ExportDataRequest {
  includeStories: boolean;
  includeImages: boolean;
  includeAudio: boolean;
  format: 'json' | 'zip';
}
