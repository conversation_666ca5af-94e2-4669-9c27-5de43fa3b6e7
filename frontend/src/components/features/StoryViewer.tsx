import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight, 
  BookOpen, 
  Volume2, 
  Share2, 
  Download,
  Heart,
  MoreHorizontal,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { Story, StoryPage } from '@/types/story';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { AudioPlayer, MiniAudioPlayer } from './AudioPlayer';
import { SimplePageAudioButton } from './PageAudioPlayer';
import { cn } from '@/utils/cn';

interface StoryViewerProps {
  story: Story;
  autoPlay?: boolean;
  showControls?: boolean;
  className?: string;
  onPageChange?: (pageNumber: number) => void;
  onComplete?: () => void;
}

export const StoryViewer: React.FC<StoryViewerProps> = ({
  story,
  autoPlay = false,
  showControls = true,
  className,
  onPageChange,
  onComplete,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [isFavorited, setIsFavorited] = useState(false);

  const totalPages = story.pages?.length || 0;
  const currentStoryPage = story.pages?.[currentPage];

  useEffect(() => {
    onPageChange?.(currentPage);
  }, [currentPage, onPageChange]);

  useEffect(() => {
    if (currentPage === totalPages - 1) {
      onComplete?.();
    }
  }, [currentPage, totalPages, onComplete]);

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 0 && pageNumber < totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const toggleFavorite = () => {
    setIsFavorited(!isFavorited);
    // TODO: 调用API更新收藏状态
  };

  const shareStory = () => {
    if (navigator.share) {
      navigator.share({
        title: story.title,
        text: `来看看这个精彩的故事：${story.title}`,
        url: window.location.href,
      });
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
    }
  };

  // 图片预加载
  useEffect(() => {
    const preloadImages = () => {
      displayPages.forEach((page) => {
        if (page.imageUrl && !loadedImages.has(page.imageUrl)) {
          const img = new Image();
          img.onload = () => {
            setLoadedImages(prev => new Set(prev).add(page.imageUrl));
          };
          img.onerror = () => {
            console.error('Failed to preload image:', page.imageUrl);
          };
          img.src = page.imageUrl;
        }
      });
    };

    if (displayPages.length > 0) {
      preloadImages();
    }
  }, [displayPages, loadedImages]);

  const downloadStory = () => {
    // TODO: 实现故事下载功能
    console.log('Download story:', story.id);
  };

  return (
    <div className={cn(
      'relative',
      isFullscreen ? 'fixed inset-0 z-50 bg-black' : '',
      className
    )}>
      {/* Header */}
      {showControls && (
        <div className={cn(
          'flex items-center justify-between p-4 border-b border-gray-200',
          isFullscreen && 'bg-black text-white border-gray-700'
        )}>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
              <BookOpen className="w-4 h-4 text-primary-600" />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">{story.title}</h2>
              <p className="text-sm text-gray-500">
                第 {currentPage + 1} 页，共 {totalPages} 页
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFavorite}
              className={cn(
                'p-2',
                isFavorited && 'text-red-500'
              )}
            >
              <Heart className={cn('w-4 h-4', isFavorited && 'fill-current')} />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={shareStory}
              className="p-2"
            >
              <Share2 className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={downloadStory}
              className="p-2"
            >
              <Download className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleFullscreen}
              className="p-2"
            >
              {isFullscreen ? (
                <Minimize2 className="w-4 h-4" />
              ) : (
                <Maximize2 className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      )}

      {/* Story Content */}
      <div className={cn(
        'relative',
        isFullscreen ? 'h-screen flex flex-col' : 'min-h-96'
      )}>
        {/* Page Content */}
        <div className={cn(
          'flex-1 flex items-center justify-center p-4',
          isFullscreen && 'bg-gradient-to-b from-gray-900 to-black'
        )}>
          <div className="max-w-4xl w-full">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPage}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center"
              >
                {/* Story Image */}
                {currentStoryPage?.imageUrl && (
                  <div className="order-2 lg:order-1">
                    <div className="relative aspect-square rounded-xl overflow-hidden shadow-lg">
                      <img
                        src={currentStoryPage.imageUrl}
                        alt={`故事插图 - 第${currentPage + 1}页`}
                        className={cn(
                          "w-full h-full object-cover transition-opacity duration-300",
                          loadedImages.has(currentStoryPage.imageUrl) ? "opacity-100" : "opacity-0"
                        )}
                        onLoad={() => {
                          setLoadedImages(prev => new Set(prev).add(currentStoryPage.imageUrl));
                        }}
                        onError={() => {
                          console.error('Image load error:', currentStoryPage.imageUrl);
                        }}
                      />
                      {/* Image Loading Placeholder - 只在图片未加载时显示 */}
                      {!loadedImages.has(currentStoryPage.imageUrl) && (
                        <div className="absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 animate-pulse" />
                      )}
                    </div>
                  </div>
                )}

                {/* Story Text */}
                <div className="order-1 lg:order-2">
                  <Card className={cn(
                    'p-6',
                    isFullscreen && 'bg-gray-800 border-gray-700 text-white'
                  )}>
                    <div className="text-lg leading-relaxed text-gray-800 mb-4">
                      {currentStoryPage?.text || '正在加载故事内容...'}
                    </div>

                    {/* Audio Player */}
                    {currentStoryPage?.audioUrl && (
                      <div className="mt-4">
                        <MiniAudioPlayer
                          audioUrl={currentStoryPage.audioUrl}
                          title={`第 ${currentPage + 1} 页朗读`}
                        />
                      </div>
                    )}
                  </Card>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className={cn(
          'flex items-center justify-between p-4 border-t border-gray-200',
          isFullscreen && 'bg-gray-900 border-gray-700'
        )}>
          <Button
            variant="outline"
            onClick={goToPreviousPage}
            disabled={currentPage === 0}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>上一页</span>
          </Button>

          {/* Page Indicators */}
          <div className="flex items-center space-x-2">
            {(story.pages || []).map((_, index) => (
              <button
                key={index}
                onClick={() => goToPage(index)}
                className={cn(
                  'w-3 h-3 rounded-full transition-colors',
                  index === currentPage
                    ? 'bg-primary-500'
                    : 'bg-gray-300 hover:bg-gray-400'
                )}
                aria-label={`跳转到第 ${index + 1} 页`}
              />
            ))}
          </div>

          <Button
            variant="outline"
            onClick={goToNextPage}
            disabled={currentPage === totalPages - 1}
            className="flex items-center space-x-2"
          >
            <span>下一页</span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Full Story Audio Player */}
      {story.audioUrl && showControls && (
        <div className={cn(
          'p-4 border-t border-gray-200',
          isFullscreen && 'bg-gray-900 border-gray-700'
        )}>
          <AudioPlayer
            audioUrl={story.audioUrl}
            title={story.title}
            subtitle={`${story.characterName}的故事`}
            coverImage={story.coverImageUrl}
            showDownload={true}
          />
        </div>
      )}

      {/* Keyboard Navigation */}
      <div
        className="sr-only"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'ArrowLeft') goToPreviousPage();
          if (e.key === 'ArrowRight') goToNextPage();
          if (e.key === 'Escape' && isFullscreen) setIsFullscreen(false);
        }}
      >
        使用左右箭头键翻页，ESC键退出全屏
      </div>
    </div>
  );
};

// Compact story viewer for previews
export const CompactStoryViewer: React.FC<{
  story: Story;
  maxPages?: number;
  className?: string;
}> = ({ story, maxPages = 3, className }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const displayPages = (story.pages || []).slice(0, maxPages);

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900">{story.title}</h3>
        <div className="text-sm text-gray-500">
          预览 ({displayPages.length}/{story.pages?.length || 0} 页)
        </div>
      </div>

      <div className="relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentPage}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="p-4">
              {displayPages[currentPage]?.imageUrl && (
                <div className="relative w-full h-32 mb-3 bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={displayPages[currentPage].imageUrl}
                    alt={`第${currentPage + 1}页插图`}
                    className="w-full h-full object-cover transition-opacity duration-300"
                    loading="lazy"
                    onLoad={(e) => {
                      e.currentTarget.style.opacity = '1';
                    }}
                    onError={(e) => {
                      e.currentTarget.style.opacity = '0.5';
                      console.error('Image load error:', displayPages[currentPage].imageUrl);
                    }}
                    style={{
                      opacity: loadedImages.has(displayPages[currentPage].imageUrl) ? 1 : 0
                    }}
                  />
                </div>
              )}

              {/* 页面内容和音频播放 */}
              <div className="space-y-3">
                <p className="text-sm text-gray-700 line-clamp-3">
                  {displayPages[currentPage]?.text}
                </p>

                {/* 页面音频播放按钮 */}
                {story.audioUrl ? (
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <span className="text-xs text-gray-500">
                      第{currentPage + 1}页朗读
                    </span>
                    <SimplePageAudioButton
                      audioUrl={story.audioUrl}
                      pageNumber={currentPage + 1}
                      className="ml-2"
                    />
                  </div>
                ) : (
                  // 临时调试信息
                  <div className="text-xs text-gray-400 pt-2 border-t border-gray-100">
                    调试: audioUrl = {JSON.stringify(story.audioUrl)} | 故事ID: {story.id}
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        </AnimatePresence>

        {displayPages.length > 1 && (
          <div className="flex justify-center mt-3 space-x-2">
            {displayPages.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={cn(
                  'w-2 h-2 rounded-full transition-colors',
                  index === currentPage ? 'bg-primary-500' : 'bg-gray-300'
                )}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
