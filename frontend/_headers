# Security headers for Cloudflare Pages
/*
  # Security headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  Cross-Origin-Opener-Policy: same-origin-allow-popups
  Cross-Origin-Embedder-Policy: unsafe-none

  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://accounts.google.com https://gsi.gstatic.com https://js.stripe.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://accounts.google.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://storyweaver-api.stawky.workers.dev https://accounts.google.com https://api.stripe.com; frame-src https://accounts.google.com https://js.stripe.com;

# Cache headers for static assets
/assets/*
  Cache-Control: public, max-age=********, immutable

# Cache headers for images
*.png
  Cache-Control: public, max-age=********
*.jpg
  Cache-Control: public, max-age=********
*.jpeg
  Cache-Control: public, max-age=********
*.gif
  Cache-Control: public, max-age=********
*.webp
  Cache-Control: public, max-age=********
*.svg
  Cache-Control: public, max-age=********

# Cache headers for fonts
*.woff
  Cache-Control: public, max-age=********
*.woff2
  Cache-Control: public, max-age=********
*.ttf
  Cache-Control: public, max-age=********
*.eot
  Cache-Control: public, max-age=********

# No cache for HTML files
*.html
  Cache-Control: no-cache

# Service worker
/sw.js
  Cache-Control: no-cache
